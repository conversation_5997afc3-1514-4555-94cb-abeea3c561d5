@media (max-width: 768px) {
  .main-content .row {
    flex-direction: column !important;
  }
  .col-8, .col-4 {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }
  .map-container {
    width: 100% !important; 
    min-height: 572px;
    margin-bottom: 1.5em;
  }
  .sidebar {
    width: 100% !important; 
    margin-bottom: 2em;
  }
  .row.mt-4.justify-content-center {
    flex-direction: column !important;
    align-items: stretch !important;
    margin-top: 1em !important;
  }
  .big-btn {
    width: 100% !important;
    height: 80px !important;
    font-size: 1.1em;
    margin: 0 0 1em 0 !important;
    border-radius: 16px;
  }
  .logo-container {
    padding: 0.25em 0.75em 0.25em 0.25em;
  }
  .balances-bar {
    flex-wrap: wrap;
    font-size: 1em; 
  }
  .footer {
    font-size: 0.95em;
    padding: 1em 0;
  }
}
.nav-btn-wrapper {
  position: relative;
  display: inline-block;
}
.nav-container {
  /* position: relative; */
}
.custom-nav-dropdown {
  position: absolute;
  top: 100%;
  right: 5px;
  margin-top: 0.5rem;
  z-index: 1000;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  display: flex;
  width: 140px;
}
.custom-nav-link a{
 text-decoration: none !important;
}
.custom-nav-link {
     height: 48px;
    min-width: 48px;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    color: #222;
    transition: background 0.2s, box-shadow 0.2s;
}
.custom-nav-link:last-child {
  margin-bottom: 0;
}
.custom-nav-link:hover, .custom-nav-link:focus {
  background: #e0e0e0;
  color: #111;
  outline: none;
}
@media (max-width: 600px) {
  .custom-nav-dropdown {
    min-width: 100px;
    padding: 0.25rem 0.5rem;
  }
  .custom-nav-link {
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
    font-size: 0.95rem;
  }
} 