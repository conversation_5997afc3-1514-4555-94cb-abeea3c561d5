/* Animations from style.html */
@keyframes zoomIn {
    0% {
        opacity: 0;
        height: 1px;
        width: 10px;
        transform: scale(0.1) rotateX(45deg) translateZ(-200px);
        border-radius: 20px;
    }
    40% {
        opacity: 0.8;
        height: 160px;
        width: 100%;
        transform: scale(1.05) rotateX(0) translateZ(10px);
        border-radius: 8px;
    }
    100% {
        opacity: 1;
        height: 150px;
        width: 100%;
        transform: scale(1) rotateX(0) translateZ(0);
        border-radius: 5px;
    }
}
@keyframes gentleBobble {
    0% { transform: scale(1) translateZ(0); }
    50% { transform: scale(1.008) translateZ(2px); }
    100% { transform: scale(1) translateZ(0); }
}
.animated-box {
    animation: zoomIn 0.7s cubic-bezier(0.2, 0.9, 0.3, 1.3) forwards, gentleBobble 0.2s 0.7s ease-out forwards;
    opacity: 0;
    transform-origin: center center;
    transform-style: preserve-3d;
    min-height: 150px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Nav and Layout Styles */
.nav-container {
    width: 100%;
    background: #fff;
    border-bottom: 1px solid #eee;
    border-radius: 0 0 20px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03); 
}
.logo-container { 
    border-bottom-right-radius: 20px;
    padding: 0.25em 1em 0.25em 0.25em;
    display: flex;
    align-items: center;
    width: auto;
}
.logo-img {
    height: 40px;
    width: auto;
    display: block;
}
.nav-btn {
    border: 1px solid #bbb;
    background: #fff;
    color: #333;
    border-radius: 8px;
    padding: 0.5em 1.5em;
    font-size: 1.1em;
    font-weight: 500;
    transition: box-shadow 0.2s;
}
.nav-btn:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.balances-bar { 
    padding: 0.5em 0; 
    margin-bottom: 2em;
}
.main-content {
    margin-top: 2em;
}
.map-container { 
    min-height: 572px;
    min-width: 592px;
    background: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 1em;
    box-sizing: border-box;
}
.sidebar {
    background: #fff;
    border: 1px solid #eee;
    border-radius: 12px;
    padding: 1em;
    min-height: 400px;
    margin-bottom: 1em;
}
.big-btn {
    width: 200px;
    height: 200px;
    font-size: 1.5em;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    margin: 0 1em;
    background: #fff;
    border: 2px solid #28a745;
    color: #28a745;
    font-weight: bold;
    transition: background 0.2s, color 0.2s;
}
.big-btn:hover {
    background: #28a745;
    color: #fff;
}

#playGamesView .row {
    flex-wrap: wrap;
    overflow-x: hidden; /* Prevent horizontal scroll */
}

#playGamesView .col {
    min-width: 0; /* Prevent columns from overflowing */
} 
 