<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <link rel="stylesheet" href="modals/modals.css">
</head>
<body>
  <div class="modal" tabindex="-1" id="newAdventureModal">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Start New Adventure</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="adventureName" class="form-label">Adventure Name</label>
              <input type="text" class="form-control" id="adventureName" placeholder="Enter adventure name">
            </div>
            <div class="mb-3">
              <label for="adventureType" class="form-label">Type</label>
              <select class="form-select" id="adventureType">
                <option selected>Choose type</option>
                <option value="1">Quest</option>
                <option value="2">Expedition</option>
                <option value="3">Challenge</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer d-flex justify-content-center">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">CANCEL</button>
          <button type="button" class="btn btn-primary">START</button>
        </div>
      </div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // To show the modal, use:
    // var newAdventureModal = new bootstrap.Modal(document.getElementById('newAdventureModal'));
    // newAdventureModal.show();
  </script>
</body>
</html> 