<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cute Crushies</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="animations.css">
    <link rel="stylesheet" href="mobile.css">
    <link rel="stylesheet" href="modals/modals.css">
    <style>
        .status-icon {
            cursor: pointer;
        }
        .destination-text {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .new-adventure-btn {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .food-indent {
            margin-left: 20px;
        }
        .creature-section {
            border-bottom: 1px solid #dee2e6;
            padding: 10px 0;
        }
        .btn-outline-secondary:hover {
            background-color: #f8f9fa;
        }
        .vehicle-card {
            animation: blinkBorder 2s infinite;
            cursor: pointer;
        }
        @keyframes blinkBorder {
            0% { border: 2px solid #28a745; }
            50% { border: 2px solid #ffffff; }
            100% { border: 2px solid #28a745; }
        }
        .team-select {
            background-color: #28a745 !important;
            color: #ffffff !important;
        }
        .team-select .destination-text,
        .team-select .progress-bar,
        .team-select .bi {
            color: #ffffff !important;
            background-color: #218838 !important;
        }
        .team-ready {
            border: 2px solid #28a745;
            position: relative;
        }
        .team-ready::after {
            content: 'Ready';
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: #28a745;
            color: #ffffff;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.7rem;
        }
        .action-buttons .big-btn {
            width: 150px;
            height: 150px;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }
        .reward-btn {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="nav-container d-flex justify-content-between align-items-center p-3 bg-light">
        <div class="logo-container">
            <img src="https://express-crushie.herokuapp.com/images/ui/logos/cc-logo-25.png" alt="Cute Crushies Logo" class="logo-img" style="height: 40px;">
        </div>
        <div class="nav-btn-wrapper">
            <button id="customNavBtn" class="nav-btn btn btn-outline-primary" type="button" aria-expanded="false" aria-controls="customNavDropdown">
                Menu
            </button>
            <div id="customNavDropdown" class="custom-nav-dropdown d-none flex-column align-items-start p-2 bg-white border rounded shadow-sm">
                <button class="custom-nav-link btn btn-link" onclick="showPlayGames()">Play Games</button>
                <button class="custom-nav-link btn btn-link">Profile</button>
                <button class="custom-nav-link btn btn-link">Settings</button>
            </div>
        </div>
    </nav>

    <!-- Player Balances -->
    <div class="container-fluid bg-light balances-bar d-flex align-items-center mb-4 p-3">
        <span class="me-3">gxp: 100</span>
        <span class="me-3">xp: 200</span>
        <span class="me-3">level: 5</span>
        <span class="me-3">wax: 50</span>
        <span>dust: 75</span>
    </div>

    <!-- Main Content Layout -->
    <div class="container main-content">
        <!-- Action Buttons -->
        <div class="action-buttons d-flex justify-content-center mb-4">
            <button class="big-btn animated-box" id="playGamesBtn">Play Games</button>
            <button class="big-btn animated-box" id="createTeamBtn" data-bs-toggle="modal" data-bs-target="#createTeamModal">Create Team</button>
            <button class="big-btn animated-box" id="editTeamBtn" data-bs-toggle="modal" data-bs-target="#editTeamModal" disabled>Edit Team</button>
        </div>

        <div class="row g-4">
            <!-- Map Container -->
            <div class="col-8">
                <div class="map-container animated-box border rounded p-3">
                    <img src="map-placeholder.png" alt="Map Placeholder" style="width: 100%; max-width: 572px;">
                    <div class="mt-3 text-center">
                        <p id="selectedTeamInfo" class="text-muted">No team selected</p>
                    </div>
                </div>
            </div>
            <!-- Adventures Sidebar -->
            <div class="col-4">
                <div class="sidebar animated-box border rounded p-3">
                    <h5 class="mb-3">Your Teams</h5>
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center team-row" data-team-id="1">
                            <i class="bi bi-car-front-fill me-2"></i>
                            <div class="d-flex flex-column flex-grow-1">
                                <span>Team Name 1</span>
                                <span class="destination-text">Heading to Mysterious Pond</span>
                            </div>
                            <button class="btn status-icon ms-2" data-bs-toggle="tooltip" data-bs-title="Napping">
                                <i class="bi bi-clock" style="font-size: 1.2rem;"></i>
                            </button>
                            <div class="progress ms-2" style="width: 100px;">
                                <div class="progress-bar" role="progressbar" style="width: 25%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center team-row" data-team-id="2">
                            <i class="bi bi-car-front-fill me-2"></i>
                            <div class="d-flex flex-column flex-grow-1">
                                <span>Team Name 2</span>
                                <span class="destination-text">Heading to Desert Castle</span>
                            </div>
                            <button class="btn status-icon ms-2" data-bs-toggle="tooltip" data-bs-title="Adventuring">
                                <i class="bi bi-compass" style="font-size: 1.2rem;"></i>
                            </button>
                            <div class="progress ms-2" style="width: 100px;">
                                <div class="progress-bar" role="progressbar" style="width: 50%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center team-row team-ready" data-team-id="3">
                            <i class="bi bi-car-front-fill me-2"></i>
                            <div class="d-flex flex-column flex-grow-1">
                                <span>Team Name 3</span>
                                <span class="destination-text">Ready to Adventure</span>
                            </div>
                            <button class="btn status-icon ms-2" data-bs-toggle="modal" data-bs-target="#editTeamModal" data-team-id="3" data-bs-title="Ready">
                                <i class="bi bi-pencil-square" style="font-size: 1.2rem;"></i>
                            </button>
                            <div class="progress ms-2" style="width: 100px;">
                                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center team-row" data-team-id="4">
                            <i class="bi bi-car-front-fill me-2"></i>
                            <div class="d-flex flex-column flex-grow-1">
                                <span>Team Name 4</span>
                                <span class="destination-text">Mysterious Pond: Treasure Found!</span>
                            </div>
                            <button class="btn status-icon ms-2" data-bs-toggle="modal" data-bs-target="#claimRewardModal" data-team-id="4" data-bs-title="Treasure Found">
                                <i class="bi bi-treasure-chest" style="font-size: 1.2rem;"></i>
                            </button>
                            <div class="progress ms-2" style="width: 100px;">
                                <div class="progress-bar" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Play Games View (hidden by default) -->
    <div class="container main-content" id="playGamesView" style="display: none;">
        <div class="row g-4 align-items-start">
            <div class="col-auto d-flex flex-column align-items-center justify-content-start" style="min-width: 220px;">
                <button id="backToMapBtn" class="big-btn animated-box" style="width: 200px; height: 200px; display: flex; align-items: center; justify-content: center;">
                    <i class="bi bi-arrow-left" style="font-size: 4rem; color: #28a745;"></i>
                </button>
            </div>
            <div class="col" style="max-height: 500px; overflow-y: auto;">
                <div class="row row-cols-2 g-4">
                    <div class="col"><button class="big-btn animated-box w-100">Game 1</button></div>
                    <div class="col"><button class="big-btn animated-box w-100">Game 2</button></div>
                    <div class="col"><button class="big-btn animated-box w-100">Game 3</button></div>
                    <div class="col"><button class="big-btn animated-box w-100">Game 4</button></div>
                    <div class="col"><button class="big-btn animated-box w-100">Game 5</button></div>
                    <div class="col"><button class="big-btn animated-box w-100">Game 6</button></div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer text-center py-3 mt-5 bg-light">
        © 2025 Summershiloh
    </footer>

    <!-- Create Team Modal -->
    <div class="modal" tabindex="-1" id="createTeamModal">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Team</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="teamNameInput" class="form-label">Team Name</label>
                        <input type="text" class="form-control" id="teamNameInput" placeholder="Enter team name">
                    </div>
                    <div class="d-flex justify-content-center mb-3">
                        <button type="button" class="btn btn-outline-secondary me-2"><i class="bi bi-caret-left-fill"></i></button>
                        <div class="custom-card">
                            <div class="card-title">Adventure Vehicle</div>
                            <i class="bi bi-car-front-fill" style="font-size: 2rem;"></i>
                        </div>
                        <button type="button" class="btn btn-outline-secondary ms-2"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex flex-wrap justify-content-center">
                        <div class="custom-card m-2">
                            <div class="card-title">Creature 1</div>
                            <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                        </div>
                        <div class="custom-card m-2">
                            <div class="card-title">Creature 2</div>
                            <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                        </div>
                        <div class="custom-card m-2">
                            <div class="card-title">Creature 3</div>
                            <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveNewTeamBtn">Create</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Team Modal -->
    <div class="modal" tabindex="-1" id="editTeamModal">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Team</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
                    <div class="mb-3">
                        <p class="form-control-plaintext" id="editTeamName">Team Name</p>
                    </div>
                    <div class="mb-3">
                        <div class="custom-card vehicle-card text-center" data-bs-toggle="tooltip" data-bs-title="Click to change vehicle">
                            <div class="card-title">Adventure Vehicle</div>
                            <i class="bi bi-car-front-fill" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <div class="accordion" id="creatureAccordion">
                        <div class="accordion-item creature-section">
                            <h2 class="accordion-header" id="creatureHeading1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse1" aria-expanded="true" aria-controls="creatureCollapse1">
                                    Creature 1
                                </button>
                            </h2>
                            <div id="creatureCollapse1" class="accordion-collapse collapse show" aria-labelledby="creatureHeading1" data-bs-parent="#creatureAccordion">
                                <div class="accordion-body">
                                    <div class="d-flex align-items-center mb-2">
                                        <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="1"><i class="bi bi-caret-left-fill"></i></button>
                                        <div class="custom-card text-center">
                                            <div class="card-title">Creature 1</div>
                                            <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                                        </div>
                                        <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="1"><i class="bi bi-caret-right-fill"></i></button>
                                    </div>
                                    <div class="d-flex align-items-center food-indent">
                                        <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="1"><i class="bi bi-caret-left-fill"></i></button>
                                        <div class="custom-card text-center">
                                            <div class="card-title">Apple</div>
                                            <i class="bi bi-apple" style="font-size: 2rem;"></i>
                                        </div>
                                        <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="1"><i class="bi bi-caret-right-fill"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item creature-section">
                            <h2 class="accordion-header" id="creatureHeading2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse2" aria-expanded="false" aria-controls="creatureCollapse2">
                                    Creature 2
                                </button>
                            </h2>
                            <div id="creatureCollapse2" class="accordion-collapse collapse" aria-labelledby="creatureHeading2" data-bs-parent="#creatureAccordion">
                                <div class="accordion-body">
                                    <div class="d-flex align-items-center mb-2">
                                        <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="2"><i class="bi bi-caret-left-fill"></i></button>
                                        <div class="custom-card text-center">
                                            <div class="card-title">Creature 2</div>
                                            <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                                        </div>
                                        <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="2"><i class="bi bi-caret-right-fill"></i></button>
                                    </div>
                                    <div class="d-flex align-items-center food-indent">
                                        <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="2"><i class="bi bi-caret-left-fill"></i></button>
                                        <div class="custom-card text-center">
                                            <div class="card-title">Carrot</div>
                                            <i class="bi bi-carrot" style="font-size: 2rem;"></i>
                                        </div>
                                        <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="2"><i class="bi bi-caret-right-fill"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item creature-section">
                            <h2 class="accordion-header" id="creatureHeading3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse3" aria-expanded="false" aria-controls="creatureCollapse3">
                                    Creature 3
                                </button>
                            </h2>
                            <div id="creatureCollapse3" class="accordion-collapse collapse" aria-labelledby="creatureHeading3" data-bs-parent="#creatureAccordion">
                                <div class="accordion-body">
                                    <div class="d-flex align-items-center mb-2">
                                        <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="3"><i class="bi bi-caret-left-fill"></i></button>
                                        <div class="custom-card text-center">
                                            <div class="card-title">Creature 3</div>
                                            <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                                        </div>
                                        <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="3"><i class="bi bi-caret-right-fill"></i></button>
                                    </div>
                                    <div class="d-flex align-items-center food-indent">
                                        <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="3"><i class="bi bi-caret-left-fill"></i></button>
                                        <div class="custom-card text-center">
                                            <div class="card-title">Berry</div>
                                            <i class="bi bi-flower1" style="font-size: 2rem;"></i>
                                        </div>
                                        <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="3"><i class="bi bi-caret-right-fill"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveTeamBtn">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Claim Reward Modal -->
    <div class="modal" tabindex="-1" id="claimRewardModal">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Claim Reward</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Congratulations! Team <span id="rewardTeamName">Team Name</span> found treasure at Mysterious Pond!</p>
                    <ul>
                        <li>100 GXP</li>
                        <li>50 Wax</li>
                        <li>Rare Berry</li>
                    </ul>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <button type="button" class="btn btn-primary" id="claimRewardBtn">Claim</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Navigation bar logic
        const navBtn = document.getElementById('customNavBtn');
        const navDropdown = document.getElementById('customNavDropdown');
        navBtn.addEventListener('click', function() {
            navDropdown.classList.toggle('d-none');
        });
        document.addEventListener('click', function(e) {
            if (!navBtn.contains(e.target) && !navDropdown.contains(e.target)) {
                navDropdown.classList.add('d-none');
            }
        });

        // View toggle logic
        const playGamesView = document.getElementById('playGamesView');
        const mapContainer = document.querySelector('.map-container');
        const actionButtons = document.querySelector('.action-buttons');
        const sidebar = document.querySelector('.sidebar');
        const playGamesBtn = document.getElementById('playGamesBtn');
        const backToMapBtn = document.getElementById('backToMapBtn');
        function showPlayGames() {
            mapContainer.style.display = 'none';
            actionButtons.style.display = 'none';
            sidebar.style.display = 'none';
            playGamesView.style.display = '';
        }
        playGamesBtn.addEventListener('click', showPlayGames);
        backToMapBtn.addEventListener('click', function() {
            mapContainer.style.display = '';
            actionButtons.style.display = '';
            sidebar.style.display = '';
            playGamesView.style.display = 'none';
        });

        // Team selection logic
        let selectedTeamId = null;
        const editTeamBtn = document.getElementById('editTeamBtn');
        const selectedTeamInfo = document.getElementById('selectedTeamInfo');
        document.querySelectorAll('.team-row').forEach(row => {
            row.addEventListener('click', function(e) {
                if (e.target.closest('.status-icon')) return;
                document.querySelectorAll('.team-row').forEach(r => r.classList.remove('team-select'));
                this.classList.add('team-select');
                selectedTeamId = this.getAttribute('data-team-id');
                editTeamBtn.disabled = false;
                selectedTeamInfo.textContent = `Selected: Team Name ${selectedTeamId} - Click a destination on the map to start adventure`;
            });
        });

        // Create Team Modal logic
        const createTeamModal = new bootstrap.Modal(document.getElementById('createTeamModal'));
        document.getElementById('saveNewTeamBtn').addEventListener('click', function() {
            const teamName = document.getElementById('teamNameInput').value || 'New Team';
            console.log(`Created team: ${teamName}`);
            // TODO: Add logic to save new team
            createTeamModal.hide();
        });

        // Edit Team Modal logic
        const editTeamModal = new bootstrap.Modal(document.getElementById('editTeamModal'));
        document.querySelectorAll('.status-icon[data-bs-target="#editTeamModal"]').forEach(button => {
            button.addEventListener('click', function() {
                const teamId = this.getAttribute('data-team-id');
                document.getElementById('editTeamName').textContent = `Team Name ${teamId}`;
                editTeamModal.show();
            });
        });

        // Claim Reward Modal logic
        const claimRewardModal = new bootstrap.Modal(document.getElementById('claimRewardModal'));
        document.querySelectorAll('.status-icon[data-bs-target="#claimRewardModal"]').forEach(button => {
            button.addEventListener('click', function() {
                const teamId = this.getAttribute('data-team-id');
                document.getElementById('rewardTeamName').textContent = `Team Name ${teamId}`;
                claimRewardModal.show();
            });
        });
        document.getElementById('claimRewardBtn').addEventListener('click', function() {
            console.log(`Rewards claimed for team ${selectedTeamId}`);
            // TODO: Add logic to claim rewards
            claimRewardModal.hide();
        });

        // Creature navigation data (3 creatures per team)
        const creatures = [
            { name: 'Creature 1', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 2', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 3', icon: 'bi-emoji-laughing-fill' }
        ];
        const creatureIndices = { 1: 0, 2: 1, 3: 2 };

        document.querySelectorAll('.creature-prev').forEach(button => {
            button.addEventListener('click', function() {
                const creatureId = this.getAttribute('data-creature-id');
                creatureIndices[creatureId] = (creatureIndices[creatureId] - 1 + creatures.length) % creatures.length;
                updateCreature(creatureId);
            });
        });
        document.querySelectorAll('.creature-next').forEach(button => {
            button.addEventListener('click', function() {
                const creatureId = this.getAttribute('data-creature-id');
                creatureIndices[creatureId] = (creatureIndices[creatureId] + 1) % creatures.length;
                updateCreature(creatureId);
            });
        });

        function updateCreature(creatureId) {
            const index = creatureIndices[creatureId];
            const creature = creatures[index];
            const creatureSection = document.querySelector(`.creature-prev[data-creature-id="${creatureId}"]`).closest('.creature-section');
            const creatureCard = creatureSection.querySelector('.custom-card');
            const accordionButton = creatureSection.querySelector('.accordion-button');
            creatureCard.querySelector('.card-title').textContent = creature.name;
            accordionButton.textContent = creature.name;
            const icon = creatureCard.querySelector('i');
            icon.className = `bi ${creature.icon}`;
            icon.style.fontSize = '2rem';
        }

        // Food navigation data
        const foods = [
            { name: 'Apple', icon: 'bi-apple' },
            { name: 'Carrot', icon: 'bi-carrot' },
            { name: 'Berry', icon: 'bi-flower1' }
        ];
        const foodIndices = { 1: 0, 2: 1, 3: 2 };

        document.querySelectorAll('.food-prev').forEach(button => {
            button.addEventListener('click', function() {
                const foodId = this.getAttribute('data-food-id');
                foodIndices[foodId] = (foodIndices[foodId] - 1 + foods.length) % foods.length;
                updateFood(foodId);
            });
        });
        document.querySelectorAll('.food-next').forEach(button => {
            button.addEventListener('click', function() {
                const foodId = this.getAttribute('data-food-id');
                foodIndices[foodId] = (foodIndices[foodId] + 1) % foods.length;
                updateFood(foodId);
            });
        });

        function updateFood(foodId) {
            const index = foodIndices[foodId];
            const food = foods[index];
            const foodCard = document.querySelector(`.food-prev[data-food-id="${foodId}"]`).parentElement.querySelector('.custom-card');
            foodCard.querySelector('.card-title').textContent = food.name;
            const icon = foodCard.querySelector('i');
            icon.className = `bi ${food.icon}`;
            icon.style.fontSize = '2rem';
        }

        // Save team logic
        document.getElementById('saveTeamBtn').addEventListener('click', function() {
            const teamData = {
                teamName: document.getElementById('editTeamName').textContent,
                vehicle: 'Adventure Vehicle',
                creatures: Object.keys(creatureIndices).map(id => ({
                    id: id,
                    name: creatures[creatureIndices[id]].name,
                    food: foods[foodIndices[id]].name
                }))
            };
            console.log('Saved team:', teamData);
            // TODO: Replace with backend API call
            editTeamModal.hide();
        });

        // Initialize Bootstrap tooltips
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
    </script>
</body>
</html>