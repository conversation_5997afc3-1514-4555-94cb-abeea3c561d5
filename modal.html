<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="modal" tabindex="-1" id="myModal">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="btn btn-outline-secondary"><i class="bi bi-caret-left-fill"></i></button>
          <div class="custom-card">
            <div class="card-title">Vehicle name</div>
            <i class="bi bi-car-front-fill" style="font-size: 2rem;"></i>
          </div>
          <button type="button" class="btn btn-outline-secondary"><i class="bi bi-caret-right-fill"></i></button>
        </div>
        <div class="modal-body d-flex justify-content-center">
          <button type="button" class="btn btn-outline-secondary me-2"><i class="bi bi-caret-left-fill"></i></button>
          <div class="custom-card">
            <div class="card-title">Creature name</div>
            <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
          </div>
          <div class="custom-card">
            <div class="card-title">Creature name</div>
            <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
          </div>
          <div class="custom-card">
            <div class="card-title">Creature name</div>
            <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
          </div>
          <button type="button" class="btn btn-outline-secondary ms-2"><i class="bi bi-caret-right-fill"></i></button>
        </div>
        <div class="modal-footer d-flex justify-content-center">
          <button type="button" class="btn btn-secondary">CANCEL</button>
          <button type="button" class="btn btn-primary">CONFIRM</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    var myModal = new bootstrap.Modal(document.getElementById('myModal'));
    myModal.show();
  </script>
</body>
</html>