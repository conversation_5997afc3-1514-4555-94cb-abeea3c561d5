<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cute Crushies</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="animations.css">
    <link rel="stylesheet" href="mobile.css">
    <link rel="stylesheet" href="modals/modals.css">
    <style>
        .status-icon {
            cursor: pointer;
        }
        .destination-text {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .new-adventure-btn {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .food-indent {
            margin-left: 20px; /* Indentation for food items */
        }
        .creature-section {
            border-bottom: 1px solid #dee2e6; /* Separator between creatures */
            padding: 10px 0;
        }
        .btn-outline-secondary:hover {
            background-color: #f8f9fa; /* Subtle hover effect */
        }
        /* Blinking border for vehicle */
        .vehicle-card {
            animation: blinkBorder 2s infinite;
            cursor: pointer;
        }
        @keyframes blinkBorder {
            0% { border: 2px solid #28a745; } /* Green */
            50% { border: 2px solid #ffffff; } /* White */
            100% { border: 2px solid #28a745; } /* Green */
        }
        /* Team select style for sidebar row */
        .team-select {
            background-color: #28a745 !important; /* Green background */
            color: #ffffff !important; /* White text */
        }
        .team-select .destination-text,
        .team-select .progress-bar,
        .team-select .bi {
            color: #ffffff !important; /* White for child elements */
            background-color: #218838 !important; /* Slightly darker green for progress bar */
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="nav-container d-flex justify-content-between align-items-center">
        <div class="logo-container">
            <img src="https://express-crushie.herokuapp.com/images/ui/logos/cc-logo-25.png" alt="Cute Crushies Logo" class="logo-img">
        </div>
        <div class="nav-btn-wrapper">
            <button id="customNavBtn" class="nav-btn btn" type="button">
                Nav -
            </button>
            <div id="customNavDropdown" class="custom-nav-dropdown d-none flex-column align-items-start">
                <button class="custom-nav-link">Link 1</button>
                <button class="custom-nav-link">Link 2</button>
                <button class="custom-nav-link">Link 3</button>
            </div>
        </div>
    </nav>

    <!-- Player Balances -->
    <div class="container-fluid bg-light balances-bar d-flex align-items-center mb-4">
        <span class="me-3">gxp: 100</span>
        <span class="me-3">xp: 200</span>
        <span class="me-3">level: 5</span>
        <span class="me-3">wax: 50</span>
        <span>dust: 75</span>
    </div>

    <!-- Main Content Layout -->
    <div class="container main-content">
        <div class="row g-4">
            <!-- Map Container -->
            <div class="col-8">
                <div class="map-container animated-box">
                    <img src="map-placeholder.png" alt="Map Placeholder" style="width:572px">
                </div>
                <div class="row mt-4 justify-content-center">
                    <div class="col-auto">
                        <button class="big-btn animated-box me-4" id="newAdventureBtn">New Adventure</button>
                    </div>
                    <div class="col-auto">
                        <button class="big-btn animated-box me-4" id="miniSelectModalBtn">Mini Select Modal</button>
                    </div>
                    <div class="col-auto">
                        <button class="big-btn animated-box" id="playGamesBtn">Play Games</button>
                    </div>
                </div>
            </div>
            <!-- Adventures Sidebar -->
            <div class="col-4">
                <div class="sidebar animated-box">
                    <div class="list-group">
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <i class="bi bi-car-front-fill me-2"></i>
                            <div class="d-flex flex-column">
                                <span>Team Name 1</span>
                                <span class="destination-text">Heading to Mysterious Pond</span>
                            </div>
                            <button class="btn status-icon ms-2" data-bs-toggle="tooltip" data-bs-title="napping">
                                <i class="bi bi-clock" style="font-size: 1.2rem;"></i>
                            </button>
                            <div class="progress" style="width: 100px;">
                                <div class="progress-bar" role="progressbar" style="width: 25%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <i class="bi bi-car-front-fill me-2"></i>
                            <div class="d-flex flex-column">
                                <span>Team Name 2</span>
                                <span class="destination-text">Heading to Desert Castle</span>
                            </div>
                            <button class="btn status-icon ms-2" data-bs-toggle="tooltip" data-bs-title="adventuring">
                                <i class="bi bi-compass" style="font-size: 1.2rem;"></i>
                            </button>
                            <div class="progress" style="width: 100px;">
                                <div class="progress-bar" role="progressbar" style="width: 50%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center team-row" data-team-id="3">
                            <i class="bi bi-car-front-fill me-2"></i>
                            <div class="d-flex flex-column">
                                <span>Team Name 3</span>
                                <span class="destination-text">Ready to Adventure</span>
                            </div>
                            <button class="btn status-icon ms-2" data-bs-toggle="modal" data-bs-target="#editTeamModal" data-team-id="3" data-bs-title="ready">
                                <i class="bi bi-pencil-square" style="font-size: 1.2rem;"></i>
                            </button>
                            <div class="progress" style="width: 100px;">
                                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center team-row" data-team-id="4">
                            <i class="bi bi-car-front-fill me-2"></i>
                            <div class="d-flex flex-column">
                                <span>Team Name 4</span>
                                <span class="destination-text">Mysterious Pond: Treasure Found!</span>
                            </div>
                            <button class="btn status-icon ms-2" data-bs-toggle="modal" data-bs-target="#editTeamModal" data-team-id="4" data-bs-title="treasure found">
                                <i class="bi bi-clock me-1" style="font-size: 1.2rem;"></i>
                                <i class="bi bi-treasure-chest" style="font-size: 1.2rem;"></i>
                            </button>
                            <div class="progress" style="width: 100px;">
                                <div class="progress-bar" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Play Games View (hidden by default) -->
    <div class="container main-content" id="playGamesView" style="display:none;">
        <div class="row g-4 align-items-start">
            <div class="col-auto d-flex flex-column align-items-center justify-content-start" style="min-width:220px;">
                <button id="backToMapBtn" class="big-btn animated-box" style="width:200px;height:200px;display:flex;align-items:center;justify-content:center;">
                    <i class="bi bi-arrow-left" style="font-size:4rem;color:#28a745;"></i>
                </button>
            </div>
            <div class="col" style="max-height:500px;overflow-y:auto;">
                <div class="row row-cols-2 g-4">
                    <div class="col"><button class="big-btn animated-box w-100">Game 1</button></div>
                    <div class="col"><button class="big-btn animated-box w-100">Game 2</button></div>
                    <div class="col"><button class="big-btn animated-box w-100">Game 3</button></div>
                    <div class="col"><button class="big-btn animated-box w-100">Game 4</button></div>
                    <div class="col"><button class="big-btn animated-box w-100">Game 5</button></div>
                    <div class="col"><button class="big-btn animated-box w-100">Game 6</button></div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer text-center py-3 mt-5">
        © 2025 Summershiloh
    </footer>

    <!-- New Adventure Modal -->
    <div class="modal" tabindex="-1" id="newAdventureModal">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="btn btn-outline-secondary"><i class="bi bi-caret-left-fill"></i></button>
            <div class="custom-card">
              <div class="card-title">Adventure Vehicle</div>
              <i class="bi bi-car-front-fill" style="font-size: 2rem;"></i>
            </div>
            <button type="button" class="btn btn-outline-secondary"><i class="bi bi-caret-right-fill"></i></button>
          </div>
          <div class="modal-body d-flex justify-content-center">
            <button type="button" class="btn btn-outline-secondary me-2"><i class="bi bi-caret-left-fill"></i></button>
            <div class="custom-card">
              <div class="card-title">Party Creature</div>
              <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
            </div>
            <div class="custom-card">
              <div class="card-title">Party Creature</div>
              <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
            </div>
            <div class="custom-card">
              <div class="card-title">Party Creature</div>
              <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
            </div>
            <button type="button" class="btn btn-outline-secondary ms-2"><i class="bi bi-caret-right-fill"></i></button>
          </div>
          <div class="modal-footer d-flex justify-content-center">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">CANCEL</button>
            <button type="button" class="btn btn-primary">START</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Team Modal -->
    <div class="modal" tabindex="-1" id="editTeamModal">
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Edit Team</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
            <div class="mb-3">
              <p class="form-control-plaintext">Team Name 3</p>
            </div>
            <div class="mb-3">
              <div class="custom-card vehicle-card text-center" data-bs-toggle="tooltip" data-bs-title="Click the vehicle to select the team for adventure">
                <div class="card-title">Adventure Vehicle</div>
                <i class="bi bi-car-front-fill" style="font-size: 2rem;"></i>
              </div>
            </div>
            <div class="accordion" id="creatureAccordion">
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading1">
                  <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse1" aria-expanded="true" aria-controls="creatureCollapse1">
                    Creature 1
                  </button>
                </h2>
                <div id="creatureCollapse1" class="accordion-collapse collapse show" aria-labelledby="creatureHeading1" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="1"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 1</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="1"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="1"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Apple</div>
                        <i class="bi bi-apple" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="1"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading2">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse2" aria-expanded="false" aria-controls="creatureCollapse2">
                    Creature 2
                  </button>
                </h2>
                <div id="creatureCollapse2" class="accordion-collapse collapse" aria-labelledby="creatureHeading2" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="2"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 2</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="2"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="2"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Carrot</div>
                        <i class="bi bi-carrot" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="2"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading3">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse3" aria-expanded="false" aria-controls="creatureCollapse3">
                    Creature 3
                  </button>
                </h2>
                <div id="creatureCollapse3" class="accordion-collapse collapse" aria-labelledby="creatureHeading3" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="3"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 3</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="3"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="3"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Berry</div>
                        <i class="bi bi-flower1" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="3"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading4">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse4" aria-expanded="false" aria-controls="creatureCollapse4">
                    Creature 4
                  </button>
                </h2>
                <div id="creatureCollapse4" class="accordion-collapse collapse" aria-labelledby="creatureHeading4" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="4"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 4</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="4"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="4"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Apple</div>
                        <i class="bi bi-apple" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="4"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading5">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse5" aria-expanded="false" aria-controls="creatureCollapse5">
                    Creature 5
                  </button>
                </h2>
                <div id="creatureCollapse5" class="accordion-collapse collapse" aria-labelledby="creatureHeading5" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="5"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 5</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="5"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="5"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Carrot</div>
                        <i class="bi bi-carrot" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="5"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading6">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse6" aria-expanded="false" aria-controls="creatureCollapse6">
                    Creature 6
                  </button>
                </h2>
                <div id="creatureCollapse6" class="accordion-collapse collapse" aria-labelledby="creatureHeading6" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="6"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 6</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="6"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="6"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Berry</div>
                        <i class="bi bi-flower1" style标准输出:="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="6"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading7">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse7" aria-expanded="false" aria-controls="creatureCollapse7">
                    Creature 7
                  </button>
                </h2>
                <div id="creatureCollapse7" class="accordion-collapse collapse" aria-labelledby="creatureHeading7" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="7"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 7</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="7"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="7"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Apple</div>
                        <i class="bi bi-apple" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="7"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading8">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse8" aria-expanded="false" aria-controls="creatureCollapse8">
                    Creature 8
                  </button>
                </h2>
                <div id="creatureCollapse8" class="accordion-collapse collapse" aria-labelledby="creatureHeading8" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="8"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 8</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="8"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="8"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Carrot</div>
                        <i class="bi bi-carrot" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="8"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading9">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse9" aria-expanded="false" aria-controls="creatureCollapse9">
                    Creature 9
                  </button>
                </h2>
                <div id="creatureCollapse9" class="accordion-collapse collapse" aria-labelledby="creatureHeading9" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="9"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 9</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="9"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="9"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Berry</div>
                        <i class="bi bi-flower1" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="9"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading10">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse10" aria-expanded="false" aria-controls="creatureCollapse10">
                    Creature 10
                  </button>
                </h2>
                <div id="creatureCollapse10" class="accordion-collapse collapse" aria-labelledby="creatureHeading10" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="10"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 10</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="10"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="10"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Apple</div>
                        <i class="bi bi-apple" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="10"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading11">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse11" aria-expanded="false" aria-controls="creatureCollapse11">
                    Creature 11
                  </button>
                </h2>
                <div id="creatureCollapse11" class="accordion-collapse collapse" aria-labelledby="creatureHeading11" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="11"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 11</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="11"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="11"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Carrot</div>
                        <i class="bi bi-carrot" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="11"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item creature-section">
                <h2 class="accordion-header" id="creatureHeading12">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#creatureCollapse12" aria-expanded="false" aria-controls="creatureCollapse12">
                    Creature 12
                  </button>
                </h2>
                <div id="creatureCollapse12" class="accordion-collapse collapse" aria-labelledby="creatureHeading12" data-bs-parent="#creatureAccordion">
                  <div class="accordion-body">
                    <div class="d-flex align-items-center mb-2">
                      <button type="button" class="btn btn-outline-secondary me-2 creature-prev" data-creature-id="12"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Creature 12</div>
                        <i class="bi bi-emoji-laughing-fill" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 creature-next" data-creature-id="12"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                    <div class="d-flex align-items-center food-indent">
                      <button type="button" class="btn btn-outline-secondary me-2 food-prev" data-food-id="12"><i class="bi bi-caret-left-fill"></i></button>
                      <div class="custom-card text-center">
                        <div class="card-title">Berry</div>
                        <i class="bi bi-flower1" style="font-size: 2rem;"></i>
                      </div>
                      <button type="button" class="btn btn-outline-secondary ms-2 food-next" data-food-id="12"><i class="bi bi-caret-right-fill"></i></button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer d-flex justify-content-center">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">CANCEL</button>
            <button type="button" class="btn btn-primary" id="saveTeamBtn">SAVE</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Mini Select Modal (for testing) -->
    <div class="modal" tabindex="-1" id="miniSelectModal">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Team Name</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="d-flex align-items-center justify-content-center">
              <button type="button" class="btn btn-outline-secondary me-3"><i class="bi bi-caret-left-fill"></i></button>
              <div class="custom-card d-flex flex-column align-items-center justify-content-center px-4 py-2">
                <i class="bi bi-car-front-fill" style="font-size: 2rem;"></i>
                <i class="bi bi-info-circle-fill" style="font-size: 1.5rem;"></i>
              </div>
              <button type="button" class="btn btn-outline-secondary ms-3"><i class="bi bi-caret-right-fill"></i></button>
            </div>
          </div>
          <div class="modal-footer d-flex justify-content-center">
            <button type="button" class="btn btn-primary">OK</button>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Navigation bar logic
        const navBtn = document.getElementById('customNavBtn');
        const navDropdown = document.getElementById('customNavDropdown');
        navBtn.addEventListener('click', function(e) {
            navDropdown.classList.toggle('d-none');
        });
        document.addEventListener('click', function(e) {
            if (!navBtn.contains(e.target) && !navDropdown.contains(e.target)) {
                navDropdown.classList.add('d-none');
            }
        });

        // Show New Adventure Modal
        const newAdventureBtn = document.getElementById('newAdventureBtn');
        const newAdventureModal = new bootstrap.Modal(document.getElementById('newAdventureModal'));
        newAdventureBtn.addEventListener('click', function() {
            newAdventureModal.show();
        });

        // Show Mini Select Modal
        const miniSelectModalBtn = document.getElementById('miniSelectModalBtn');
        const miniSelectModal = new bootstrap.Modal(document.getElementById('miniSelectModal'));
        miniSelectModalBtn.addEventListener('click', function() {
            miniSelectModal.show();
        });

        // Play Games view toggle
        const playGamesBtn = document.getElementById('playGamesBtn');
        const playGamesView = document.getElementById('playGamesView');
        const mapContainer = document.querySelector('.map-container');
        const bigBtnsRow = document.querySelector('.row.mt-4.justify-content-center');
        const sidebar = document.querySelector('.sidebar');
        const backToMapBtn = document.getElementById('backToMapBtn');
        playGamesBtn.addEventListener('click', function() {
            mapContainer.style.display = 'none';
            bigBtnsRow.style.display = 'none';
            sidebar.style.display = 'none';
            playGamesView.style.display = '';
        });
        backToMapBtn.addEventListener('click', function() {
            mapContainer.style.display = '';
            bigBtnsRow.style.display = '';
            sidebar.style.display = '';
            playGamesView.style.display = 'none';
        });

        // Initialize Bootstrap tooltips
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

        // Edit Team Modal logic
        const editTeamModal = new bootstrap.Modal(document.getElementById('editTeamModal'));
        document.querySelectorAll('.status-icon[data-bs-toggle="modal"]').forEach(button => {
            button.addEventListener('click', function() {
                const teamId = this.getAttribute('data-team-id');
                const teamNameDisplay = document.querySelector('#editTeamModal .form-control-plaintext');
                teamNameDisplay.textContent = `Team Name ${teamId}`;
                editTeamModal.show();
            });
        });

        // Vehicle click logic
        document.querySelector('.vehicle-card').addEventListener('click', function() {
            console.log('Vehicle clicked for team selection');
            // TODO: Add logic to select team for adventure
        });

        // Team row click logic
        document.querySelectorAll('.team-row').forEach(row => {
            row.addEventListener('click', function(e) {
                // Prevent modal trigger if clicking the edit button
                if (e.target.closest('.status-icon')) return;
                this.classList.toggle('team-select');
            });
        });

        // Creature navigation data (12 creatures)
        const creatures = [
            { name: 'Creature 1', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 2', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 3', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 4', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 5', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 6', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 7', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 8', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 9', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 10', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 11', icon: 'bi-emoji-laughing-fill' },
            { name: 'Creature 12', icon: 'bi-emoji-laughing-fill' }
        ];
        const creatureIndices = {
            1: 0, 2: 1, 3: 2, 4: 3, 5: 4, 6: 5,
            7: 6, 8: 7, 9: 8, 10: 9, 11: 10, 12: 11
        };

        // Creature navigation event listeners
        document.querySelectorAll('.creature-prev').forEach(button => {
            button.addEventListener('click', function() {
                const creatureId = this.getAttribute('data-creature-id');
                creatureIndices[creatureId] = (creatureIndices[creatureId] - 1 + creatures.length) % creatures.length;
                updateCreature(creatureId);
            });
        });
        document.querySelectorAll('.creature-next').forEach(button => {
            button.addEventListener('click', function() {
                const creatureId = this.getAttribute('data-creature-id');
                creatureIndices[creatureId] = (creatureIndices[creatureId] + 1) % creatures.length;
                updateCreature(creatureId);
            });
        });

        function updateCreature(creatureId) {
            const index = creatureIndices[creatureId];
            const creature = creatures[index];
            const creatureSection = document.querySelector(`.creature-prev[data-creature-id="${creatureId}"]`).closest('.creature-section');
            const creatureCard = creatureSection.querySelector('.custom-card');
            const accordionButton = creatureSection.querySelector('.accordion-button');
            creatureCard.querySelector('.card-title').textContent = creature.name;
            accordionButton.textContent = creature.name;
            const icon = creatureCard.querySelector('i');
            icon.className = `bi ${creature.icon}`;
            icon.style.fontSize = '2rem';
        }

        // Food navigation data
        const foods = [
            { name: 'Apple', icon: 'bi-apple' },
            { name: 'Carrot', icon: 'bi-carrot' },
            { name: 'Berry', icon: 'bi-flower1' }
        ];
        const foodIndices = {
            1: 0, 2: 1, 3: 2, 4: 0, 5: 1, 6: 2,
            7: 0, 8: 1, 9: 2, 10: 0, 11: 1, 12: 2
        };

        // Food navigation event listeners
        document.querySelectorAll('.food-prev').forEach(button => {
            button.addEventListener('click', function() {
                const foodId = this.getAttribute('data-food-id');
                foodIndices[foodId] = (foodIndices[foodId] - 1 + foods.length) % foods.length;
                updateFood(foodId);
            });
        });
        document.querySelectorAll('.food-next').forEach(button => {
            button.addEventListener('click', function() {
                const foodId = this.getAttribute('data-food-id');
                foodIndices[foodId] = (foodIndices[foodId] + 1) % foods.length;
                updateFood(foodId);
            });
        });

        function updateFood(foodId) {
            const index = foodIndices[foodId];
            const food = foods[index];
            const foodCard = document.querySelector(`.food-prev[data-food-id="${foodId}"]`).parentElement.querySelector('.custom-card');
            foodCard.querySelector('.card-title').textContent = food.name;
            const icon = foodCard.querySelector('i');
            icon.className = `bi ${food.icon}`;
            icon.style.fontSize = '2rem';
        }

        // Save button logic (placeholder)
        document.getElementById('saveTeamBtn').addEventListener('click', function() {
            const teamData = {
                teamName: document.querySelector('#editTeamModal .form-control-plaintext').textContent,
                vehicle: 'Adventure Vehicle',
                creatures: Object.keys(creatureIndices).map(id => ({
                    id: id,
                    name: creatures[creatureIndices[id]].name,
                    food: foods[foodIndices[id]].name
                }))
            };
            console.log('Saved team:', teamData);
            // TODO: Replace with backend API call or local storage
            editTeamModal.hide();
        });
    </script>
</body>
</html>